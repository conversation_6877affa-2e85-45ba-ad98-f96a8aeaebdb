<template>
  <el-collapse-item :name="result.index">
    <template #title>
      <span class="truncate-title">Item {{ result.index + 1 }}: {{ result.title }}</span>
    </template>
    <el-row></el-row>
    <el-descriptions :column="1" border direction="vertical">
      <el-descriptions-item label="Title">
        <el-text>{{ result.title }}</el-text>
      </el-descriptions-item>
      <el-descriptions-item label="Abstract">
        <el-text>{{ getAbstractByIndex(result.index) }}</el-text>
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          Matched Tags
          <el-tooltip effect="dark" :placement="screenIsPortrait ? 'top' : 'right'">
            <template #content> This is the final list of tags for the item.<br />
              You can click a tag to deselect it, add new tags using the input box, <br />or drag and
              drop/double-click tags from other sections below to include them.</template>
            <el-icon>
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </template>

        <div class="tags-container droppable-area" @dragover="handleDragOver"
          @drop="handleDrop($event, result.index)">
          <!-- Existing matched tags -->
          <el-tag v-for="tag in result.tags.matched_tags" :key="tag"
            :class="{ 'deselected-tag': isTagDeselected(result.index, tag) }"
            class="tag-item clickable-tag" @click="toggleTag(result.index, tag)">
            {{ tag }}
          </el-tag>

          <!-- New tags with different styles -->
          <template v-for="tag in Array.from(newTags.get(result.index) || [])" :key="tag.text">
            <el-tag :type="tag.isMatched ? 'primary' : 'warning'" closable
              @close="removeNewTag(result.index, tag.text)">
              {{ tag.text }}
            </el-tag>
          </template>

          <!-- Enhanced tag input with suggestions -->
          <div class="tag-input-container">
            <el-input class="tag-input" placeholder="Type to search or Enter to add customized tags..."
              v-model="inputNewTagValue" @focus="() => handleInputFocus(result.index)"
              @input="(value) => handleSearchInput(value, result.index)"
              @keyup.enter="(event) => addNewTag(result.index, event)"
              @blur="() => closeSuggestions(result.index)" size="small"
              :loading="isFetchingTags && !hasLoadedTags">
              <template #prefix>
                <el-icon v-if="isFetchingTags && !hasLoadedTags" class="is-loading">
                  <Loading />
                </el-icon>
              </template>
            </el-input>

            <!-- Tag suggestions dropdown -->
            <div
              v-show="showTagSuggestions.get(result.index) && getFilteredTags(searchQuery.get(result.index)).length > 0"
              class="tag-suggestions-dropdown">
              <template v-if="!isFetchingTags || hasLoadedTags">
                <div v-for="tag in getFilteredTags(searchQuery.get(result.index))" :key="tag"
                  class="tag-suggestion-item" @mousedown="() => selectTag(result.index, tag)">
                  {{ tag }}
                </div>
              </template>
              <div v-else class="loading-indicator">
                Loading tags...
              </div>
            </div>
          </div>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="Concept Tags">
        <div class="tags-container">
          <el-tag v-for="tag in result.tags.concept_tags" :key="tag" type="info"
            class="tag-item draggable-tag" draggable="true"
            @dragstart="handleDragStart($event, tag, 'concept')"
            @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
            {{ tag }}
          </el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="Person/Organization Tags">
        <div class="tags-container">
          <el-tag v-for="tag in result.tags.person_org_tags" :key="tag" type="info"
            class="tag-item draggable-tag" draggable="true"
            @dragstart="handleDragStart($event, tag, 'person_org')"
            @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
            {{ tag }}
          </el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="Time/Place Tags">
        <div class="tags-container">
          <el-tag v-for="tag in result.tags.time_place_tags" :key="tag" type="info"
            class="tag-item draggable-tag" draggable="true"
            @dragstart="handleDragStart($event, tag, 'time_place')"
            @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
            {{ tag }}
          </el-tag>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </el-collapse-item>
</template>

<script setup>
import { computed } from 'vue'
import {
  ElCollapseItem,
  ElRow,
  ElDescriptions,
  ElDescriptionsItem,
  ElText,
  ElTooltip,
  ElIcon,
  ElTag,
  ElInput
} from 'element-plus'
import { InfoFilled, Loading } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  result: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  allIndexedBiblioItems: {
    type: Array,
    required: true
  },
  deselectedTags: {
    type: Set,
    required: true
  },
  newTags: {
    type: Map,
    required: true
  },
  inputNewTagValue: {
    type: String,
    required: true
  },
  showTagSuggestions: {
    type: Map,
    required: true
  },
  searchQuery: {
    type: Map,
    required: true
  },
  isFetchingTags: {
    type: Boolean,
    required: true
  },
  hasLoadedTags: {
    type: Boolean,
    required: true
  },
  screenIsPortrait: {
    type: Boolean,
    required: true
  },
  tagNames: {
    type: Array,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'update:inputNewTagValue',
  'toggleTag',
  'removeNewTag',
  'handleInputFocus',
  'handleSearchInput',
  'addNewTag',
  'closeSuggestions',
  'selectTag',
  'handleDragOver',
  'handleDrop',
  'handleDragStart',
  'addNewTagDoubleClick'
])

// Computed properties for v-model support
const inputNewTagValue = computed({
  get: () => props.inputNewTagValue,
  set: (value) => emit('update:inputNewTagValue', value)
})

// Helper methods
const getAbstractByIndex = (index) => {
  const item = props.allIndexedBiblioItems.find(item => item.index === index)
  return item ? item.abstract : ''
}

const isTagDeselected = (resultIndex, tag) => {
  return props.deselectedTags.has(`${resultIndex}-${tag}`)
}

const getFilteredTags = (query) => {
  if (!query) return []
  const lowerQuery = query.toLowerCase()
  return props.tagNames.filter(tag => 
    tag.toLowerCase().includes(lowerQuery)
  ).slice(0, 10) // Limit to 10 suggestions
}

// Event handlers
const toggleTag = (resultIndex, tag) => {
  emit('toggleTag', resultIndex, tag)
}

const removeNewTag = (resultIndex, tagText) => {
  emit('removeNewTag', resultIndex, tagText)
}

const handleInputFocus = (resultIndex) => {
  emit('handleInputFocus', resultIndex)
}

const handleSearchInput = (value, resultIndex) => {
  emit('handleSearchInput', value, resultIndex)
}

const addNewTag = (resultIndex, event) => {
  emit('addNewTag', resultIndex, event)
}

const closeSuggestions = (resultIndex) => {
  emit('closeSuggestions', resultIndex)
}

const selectTag = (resultIndex, tag) => {
  emit('selectTag', resultIndex, tag)
}

const handleDragOver = (event) => {
  emit('handleDragOver', event)
}

const handleDrop = (event, index) => {
  emit('handleDrop', event, index)
}

const handleDragStart = (event, tag, type) => {
  emit('handleDragStart', event, tag, type)
}

const addNewTagDoubleClick = (resultIndex, tag) => {
  emit('addNewTagDoubleClick', resultIndex, tag)
}
</script>

<style scoped>
.truncate-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 40px;
  padding: 8px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color-overlay);
}

.droppable-area {
  border-color: var(--el-color-primary);
  transition: border-color 0.3s;
}

.tag-item {
  margin: 2px;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.draggable-tag {
  cursor: grab;
  transition: all 0.3s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.tag-input-container {
  position: relative;
  min-width: 200px;
}

.tag-input {
  width: 100%;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.tag-suggestion-item:hover {
  background-color: var(--el-bg-color-overlay);
}

.tag-suggestion-item:last-child {
  border-bottom: none;
}

.loading-indicator {
  padding: 8px 12px;
  text-align: center;
  color: var(--el-text-color-secondary);
}
</style>
