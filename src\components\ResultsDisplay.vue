<template>
  <div>
    <!-- Results section -->
    <el-divider class="el-divider--nowrap">Step 2. Review and Edit Matched Tags</el-divider>
    <div v-if="results.length" class="results">
      <el-row justify="center" class="button-container">
        <el-col :span="24">
          <el-button plain round @click="handleToggleAllResults">
            <el-icon>
              <component :is="activeNames.length === paginatedResults.length ? ZoomOut : ZoomIn" />
            </el-icon>
            {{ activeNames.length === paginatedResults.length ? 'Collapse All' : 'Expand All' }}
          </el-button>
        </el-col>
      </el-row>
      <el-row justify="center">
        <el-col :span="24">
          <el-collapse v-model="activeNames">
            <ResultItem
              v-for="(result, index) in paginatedResults"
              :key="result.index"
              :result="result"
              :index="index"
              :allIndexedBiblioItems="allIndexedBiblioItems"
              :deselectedTags="deselectedTags"
              :newTags="newTags"
              :inputNewTagValue="inputNewTagValue"
              :showTagSuggestions="showTagSuggestions"
              :searchQuery="searchQuery"
              :isFetchingTags="isFetchingTags"
              :hasLoadedTags="hasLoadedTags"
              :screenIsPortrait="screenIsPortrait"
              :tagNames="tagNames"
              @update:inputNewTagValue="$emit('update:inputNewTagValue', $event)"
              @toggleTag="(...args) => $emit('toggleTag', ...args)"
              @removeNewTag="(...args) => $emit('removeNewTag', ...args)"
              @handleInputFocus="(...args) => $emit('handleInputFocus', ...args)"
              @handleSearchInput="(...args) => $emit('handleSearchInput', ...args)"
              @addNewTag="(...args) => $emit('addNewTag', ...args)"
              @closeSuggestions="(...args) => $emit('closeSuggestions', ...args)"
              @selectTag="(...args) => $emit('selectTag', ...args)"
              @handleDragOver="(...args) => $emit('handleDragOver', ...args)"
              @handleDrop="(...args) => $emit('handleDrop', ...args)"
              @handleDragStart="(...args) => $emit('handleDragStart', ...args)"
              @addNewTagDoubleClick="(...args) => $emit('addNewTagDoubleClick', ...args)"
            />
          </el-collapse>
        </el-col>
      </el-row>
      <!-- Pagination component -->
      <el-row justify="center" class="mt-4">
        <el-col :span="24">
          <el-pagination 
            v-model:current-page="currentPage" 
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]" 
            :total="results.length" 
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange" 
            @current-change="handleCurrentChange" 
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElDivider, ElRow, ElCol, ElButton, ElIcon, ElCollapse, ElPagination } from 'element-plus'
import { ZoomIn, ZoomOut } from '@element-plus/icons-vue'
import ResultItem from './ResultItem.vue'

// Props
const props = defineProps({
  results: {
    type: Array,
    required: true
  },
  activeNames: {
    type: Array,
    required: true
  },
  paginatedResults: {
    type: Array,
    required: true
  },
  currentPage: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    required: true
  },
  allIndexedBiblioItems: {
    type: Array,
    required: true
  },
  deselectedTags: {
    type: Set,
    required: true
  },
  newTags: {
    type: Map,
    required: true
  },
  inputNewTagValue: {
    type: String,
    required: true
  },
  showTagSuggestions: {
    type: Map,
    required: true
  },
  searchQuery: {
    type: Map,
    required: true
  },
  isFetchingTags: {
    type: Boolean,
    required: true
  },
  hasLoadedTags: {
    type: Boolean,
    required: true
  },
  screenIsPortrait: {
    type: Boolean,
    required: true
  },
  tagNames: {
    type: Array,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'update:activeNames',
  'update:currentPage',
  'update:pageSize',
  'update:inputNewTagValue',
  'toggleAllResults',
  'sizeChange',
  'currentChange',
  'toggleTag',
  'removeNewTag',
  'handleInputFocus',
  'handleSearchInput',
  'addNewTag',
  'closeSuggestions',
  'selectTag',
  'handleDragOver',
  'handleDrop',
  'handleDragStart',
  'addNewTagDoubleClick'
])

// Computed properties for v-model support
const activeNames = computed({
  get: () => props.activeNames,
  set: (value) => emit('update:activeNames', value)
})

const currentPage = computed({
  get: () => props.currentPage,
  set: (value) => emit('update:currentPage', value)
})

const pageSize = computed({
  get: () => props.pageSize,
  set: (value) => emit('update:pageSize', value)
})

// Event handlers
const handleToggleAllResults = () => {
  emit('toggleAllResults')
}

const handleSizeChange = (size) => {
  emit('sizeChange', size)
}

const handleCurrentChange = (page) => {
  emit('currentChange', page)
}
</script>

<style scoped>
.results {
  margin-top: 20px;
}

.button-container {
  margin-bottom: 20px;
}

.mt-4 {
  margin-top: 20px;
}
</style>
